#!/usr/bin/env python3
"""
Qlib数据下载脚本
用于下载和初始化Qlib所需的中国股票市场数据
"""

import os
import sys
import urllib.request
import zipfile
from pathlib import Path
from loguru import logger
import qlib
from qlib.config import REG_CN


def download_qlib_data():
    """下载Qlib数据"""
    
    # 数据存储路径
    data_dir = Path.home() / ".qlib" / "qlib_data"
    data_dir.mkdir(parents=True, exist_ok=True)
    
    # 数据下载URL（这是一个示例URL，实际使用时需要替换为正确的URL）
    data_url = "https://github.com/microsoft/qlib/releases/download/v0.9.0/qlib_data_cn_1d_latest.zip"
    zip_file = data_dir / "qlib_data_cn_1d_latest.zip"
    
    logger.info("开始下载Qlib数据...")
    logger.info(f"下载地址: {data_url}")
    logger.info(f"保存路径: {zip_file}")
    
    try:
        # 下载数据
        urllib.request.urlretrieve(data_url, zip_file)
        logger.info("数据下载完成")
        
        # 解压数据
        logger.info("开始解压数据...")
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            zip_ref.extractall(data_dir)
        
        # 删除压缩文件
        zip_file.unlink()
        logger.info("数据解压完成")
        
        return True
        
    except Exception as e:
        logger.error(f"下载数据失败: {e}")
        return False


def init_qlib():
    """初始化Qlib"""
    try:
        data_path = Path.home() / ".qlib" / "qlib_data" / "cn_data"
        
        qlib.init(
            provider_uri=str(data_path),
            region=REG_CN
        )
        
        logger.info("Qlib初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"Qlib初始化失败: {e}")
        return False


def verify_data():
    """验证数据是否正确安装"""
    try:
        from qlib.data import D
        
        # 测试获取股票列表
        instruments = D.instruments(market='csi300')
        logger.info(f"成功获取CSI300股票列表，共 {len(instruments)} 只股票")
        
        # 测试获取价格数据
        data = D.features(
            instruments=['000001.XSHE'],
            fields=['$close', '$volume'],
            start_time='2023-01-01',
            end_time='2023-01-10'
        )
        
        if not data.empty:
            logger.info("数据验证成功")
            return True
        else:
            logger.error("数据验证失败：获取的数据为空")
            return False
            
    except Exception as e:
        logger.error(f"数据验证失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始初始化Qlib数据...")
    
    # 检查数据是否已存在
    data_path = Path.home() / ".qlib" / "qlib_data" / "cn_data"
    
    if data_path.exists():
        logger.info("检测到已存在的数据，尝试初始化...")
        if init_qlib() and verify_data():
            logger.info("数据初始化成功")
            return
        else:
            logger.warning("现有数据有问题，重新下载...")
    
    # 下载数据
    if download_qlib_data():
        # 初始化Qlib
        if init_qlib():
            # 验证数据
            if verify_data():
                logger.info("Qlib数据初始化完成")
            else:
                logger.error("数据验证失败")
                sys.exit(1)
        else:
            logger.error("Qlib初始化失败")
            sys.exit(1)
    else:
        logger.error("数据下载失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
