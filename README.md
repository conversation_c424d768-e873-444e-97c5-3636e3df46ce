# VenusQuant - 量化数据分析显示系统

基于Microsoft Qlib的本地量化数据分析和显示系统，提供技术指标分析、股票筛选、回测和实时监控功能。

## 🚀 功能特性

- **技术指标分析**: 支持Alpha360和Alpha158技术指标计算和对比
- **股票筛选**: 基于ROE等财务指标的多维度股票筛选
- **数据管理**: CSI 300股票池管理和财务数据获取
- **Web界面**: 基于Streamlit的交互式数据可视化界面
- **实时监控**: 股票数据实时更新和监控
- **回测系统**: 策略回测和性能分析

## 📋 系统要求

- Python 3.8+
- 8GB+ RAM (推荐16GB)
- 10GB+ 可用磁盘空间
- 稳定的网络连接（用于数据下载）

## 🛠️ 安装和运行

### 1. 克隆项目
```bash
git clone <your-repo-url>
cd VenusQuant
```

### 2. 创建虚拟环境
```bash
# 使用conda（推荐）
conda create -n venusquant python=3.9
conda activate venusquant

# 或使用venv
python -m venv venusquant
source venusquant/bin/activate  # Linux/Mac
# venusquant\Scripts\activate  # Windows
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 初始化Qlib数据
```bash
# 下载CN股票数据（约2-3GB，需要较长时间）
python -c "import qlib; qlib.init(provider_uri='~/.qlib/qlib_data/cn_data', region='cn')"

# 如果上述命令失败，可以手动下载数据
python scripts/download_data.py
```

### 5. 运行系统
```bash
# 方式1：直接运行主程序
python main.py

# 方式2：使用Streamlit运行
streamlit run main.py

# 方式3：指定端口运行
streamlit run main.py --server.port 8501
```

### 6. 访问Web界面
打开浏览器访问：http://localhost:8501
