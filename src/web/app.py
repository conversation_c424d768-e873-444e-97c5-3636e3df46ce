"""
VenusQuant Streamlit Web应用
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime, timedelta
import yaml
from pathlib import Path
import sys
from loguru import logger

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from data import DataManager, StockPoolManager, FinancialDataManager
    from screening import StockScreener, ScreeningCriteria
except ImportError as e:
    st.error(f"导入模块失败: {e}")
    st.info("请确保已正确安装所有依赖包")
    st.stop()


def load_config():
    """加载配置文件"""
    try:
        config_path = Path(__file__).parent.parent.parent / "config" / "config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        st.error(f"加载配置文件失败: {e}")
        return {}


def initialize_qlib():
    """初始化Qlib"""
    try:
        import qlib
        from qlib.config import REG_CN
        
        config = load_config()
        if not config:
            return False
            
        qlib.init(
            provider_uri=config.get('qlib', {}).get('provider_uri', '~/.qlib/qlib_data/cn_data'),
            region=REG_CN
        )
        return True
    except Exception as e:
        st.error(f"Qlib初始化失败: {e}")
        st.info("请运行: python scripts/download_data.py 下载数据")
        return False


def main():
    """主应用函数"""
    # 页面配置
    st.set_page_config(
        page_title="VenusQuant - 量化分析系统",
        page_icon="📈",
        layout="wide"
    )
    
    # 标题
    st.title("📈 VenusQuant - 量化数据分析系统")
    
    # 初始化检查
    if 'qlib_initialized' not in st.session_state:
        with st.spinner("正在初始化Qlib..."):
            if initialize_qlib():
                st.session_state.qlib_initialized = True
                st.success("✅ Qlib初始化成功！")
            else:
                st.error("❌ Qlib初始化失败")
                st.markdown("""
                ### 解决方案：
                1. 运行数据下载脚本：`python scripts/download_data.py`
                2. 或手动下载Qlib数据到 `~/.qlib/qlib_data/cn_data`
                3. 检查网络连接是否正常
                """)
                st.stop()
    
    # 加载配置
    config = load_config()
    if not config:
        st.stop()
    
    # 侧边栏
    with st.sidebar:
        st.header("⚙️ 系统设置")
        
        # 页面选择
        page = st.selectbox(
            "选择功能页面",
            ["🏠 系统概览", "📊 数据查看", "🔍 股票筛选"],
            index=0
        )
        
        st.markdown("---")
        
        # 时间设置
        st.subheader("📅 时间设置")
        start_date = st.date_input(
            "开始日期",
            value=datetime.now() - timedelta(days=30),
            max_value=datetime.now()
        )
        end_date = st.date_input(
            "结束日期", 
            value=datetime.now(),
            max_value=datetime.now()
        )
        
        # 股票池设置
        st.subheader("📈 股票池")
        pool_type = st.selectbox("选择股票池", ["CSI300", "自定义"])
        
        stock_pool = None
        if pool_type == "自定义":
            custom_stocks = st.text_area(
                "输入股票代码（每行一个）",
                placeholder="000001.XSHE\n000002.XSHE"
            )
            if custom_stocks:
                stock_pool = [s.strip() for s in custom_stocks.split('\n') if s.strip()]
    
    # 主内容区域
    if page == "🏠 系统概览":
        show_system_overview(config)
    elif page == "📊 数据查看":
        show_data_view(config, start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d'), stock_pool)
    elif page == "🔍 股票筛选":
        show_stock_screening(config, start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d'), stock_pool)


def show_system_overview(config):
    """显示系统概览"""
    st.header("🏠 系统概览")
    
    # 系统状态
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("系统状态", "运行中", "✅")
    with col2:
        st.metric("数据源", "Qlib", "📊")
    with col3:
        st.metric("支持市场", "中国A股", "🇨🇳")
    with col4:
        st.metric("功能模块", "3个", "🔧")
    
    st.markdown("---")
    
    # 功能介绍
    st.subheader("📋 主要功能")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.info("""
        **📊 数据查看**
        - 查看股票基础数据
        - 价格走势图表
        - 成交量分析
        """)
        
        st.success("""
        **🔍 股票筛选**
        - ROE指标筛选
        - 多维度条件设置
        - 筛选结果导出
        """)
    
    with col2:
        st.warning("""
        **📈 技术指标分析**
        - Alpha360指标
        - Alpha158指标
        - 指标对比分析
        """)
        
        st.error("""
        **⚖️ 回测分析**
        - 策略回测
        - 风险评估
        - 收益分析
        """)
    
    # 配置信息
    with st.expander("⚙️ 查看系统配置"):
        st.json(config)


def show_data_view(config, start_date, end_date, stock_pool):
    """显示数据查看页面"""
    st.header("📊 数据查看")
    
    try:
        # 初始化管理器
        stock_pool_manager = StockPoolManager(config)
        data_manager = DataManager(config)
        
        # 获取股票池
        if stock_pool is None:
            with st.spinner("正在获取CSI300股票池..."):
                stocks = stock_pool_manager.get_hs300_stocks()[:20]  # 限制前20只
        else:
            stocks = stock_pool[:20]
        
        if not stocks:
            st.warning("⚠️ 未找到股票数据")
            return
        
        st.info(f"📈 当前股票池包含 {len(stocks)} 只股票")
        
        # 股票选择
        selected_stock = st.selectbox("选择要查看的股票", stocks)
        
        if selected_stock:
            with st.spinner(f"正在获取 {selected_stock} 的数据..."):
                try:
                    stock_data = data_manager.get_stock_data(
                        [selected_stock], 
                        start_date, 
                        end_date,
                        fields=['$close', '$open', '$high', '$low', '$volume']
                    )
                    
                    if not stock_data.empty and selected_stock in stock_data.index:
                        stock_prices = stock_data.loc[selected_stock]
                        
                        # 价格图表
                        fig = go.Figure()
                        fig.add_trace(go.Scatter(
                            x=stock_prices.index,
                            y=stock_prices['$close'],
                            mode='lines',
                            name='收盘价',
                            line=dict(color='#1f77b4', width=2)
                        ))
                        
                        fig.update_layout(
                            title=f"📈 {selected_stock} 价格走势",
                            xaxis_title="日期",
                            yaxis_title="价格 (元)",
                            height=400,
                            showlegend=True
                        )
                        
                        st.plotly_chart(fig, use_container_width=True)
                        
                        # 数据统计
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.metric("最新价格", f"{stock_prices['$close'].iloc[-1]:.2f}")
                        with col2:
                            st.metric("最高价", f"{stock_prices['$high'].max():.2f}")
                        with col3:
                            st.metric("最低价", f"{stock_prices['$low'].min():.2f}")
                        with col4:
                            avg_volume = stock_prices['$volume'].mean()
                            st.metric("平均成交量", f"{avg_volume:.0f}")
                        
                        # 数据表格
                        st.subheader("📋 最近数据")
                        st.dataframe(stock_prices.tail(10).round(2))
                        
                    else:
                        st.warning(f"⚠️ 未获取到 {selected_stock} 的数据")
                        
                except Exception as e:
                    st.error(f"❌ 获取数据失败: {e}")
    
    except Exception as e:
        st.error(f"❌ 数据查看功能出错: {e}")


def show_stock_screening(config, start_date, end_date, stock_pool):
    """显示股票筛选页面"""
    st.header("🔍 股票筛选")
    
    # 筛选条件设置
    st.subheader("📋 设置筛选条件")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        min_roe = st.number_input("最小ROE (%)", min_value=0.0, max_value=100.0, value=10.0, step=1.0)
    
    with col2:
        max_roe = st.number_input("最大ROE (%)", min_value=0.0, max_value=100.0, value=50.0, step=1.0)
    
    with col3:
        period = st.selectbox("ROE计算周期", ["latest", "avg_1y", "avg_3y"])
    
    if st.button("🚀 开始筛选", type="primary"):
        with st.spinner("正在筛选股票，请稍候..."):
            try:
                # 创建筛选器
                screener = StockScreener(config)
                
                # 创建筛选条件
                criteria = ScreeningCriteria().add_roe_criteria(min_roe, max_roe, period)
                
                # 执行筛选
                filtered_stocks, result_data = screener.screen_stocks(
                    criteria, stock_pool, start_date, end_date
                )
                
                if filtered_stocks:
                    st.success(f"✅ 筛选完成！找到 {len(filtered_stocks)} 只符合条件的股票")
                    
                    # 显示结果
                    st.subheader("📊 筛选结果")
                    
                    # 分列显示
                    cols = st.columns(3)
                    for i, stock in enumerate(filtered_stocks[:30]):  # 显示前30只
                        with cols[i % 3]:
                            st.write(f"**{i+1}.** {stock}")
                    
                    if len(filtered_stocks) > 30:
                        st.info(f"ℹ️ 还有 {len(filtered_stocks) - 30} 只股票未显示")
                    
                    # 下载按钮
                    if filtered_stocks:
                        stock_list = '\n'.join(filtered_stocks)
                        st.download_button(
                            label="📥 下载筛选结果",
                            data=stock_list,
                            file_name=f"filtered_stocks_{datetime.now().strftime('%Y%m%d')}.txt",
                            mime="text/plain"
                        )
                else:
                    st.warning("⚠️ 未找到符合条件的股票，请调整筛选条件")
                    
            except Exception as e:
                st.error(f"❌ 股票筛选出错: {e}")


def run_app():
    """运行Web应用"""
    try:
        main()
    except Exception as e:
        st.error(f"❌ 应用运行出错: {e}")
        logger.error(f"Web应用运行出错: {e}")


if __name__ == "__main__":
    run_app()
