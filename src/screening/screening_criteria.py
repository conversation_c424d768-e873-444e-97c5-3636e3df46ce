"""
筛选条件类
定义各种股票筛选条件
"""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from enum import Enum


class ComparisonOperator(Enum):
    """比较操作符枚举"""
    GREATER_THAN = ">"
    LESS_THAN = "<"
    GREATER_EQUAL = ">="
    LESS_EQUAL = "<="
    EQUAL = "=="
    NOT_EQUAL = "!="
    BETWEEN = "between"
    IN = "in"
    NOT_IN = "not_in"


@dataclass
class FinancialCriteria:
    """财务指标筛选条件"""
    indicator: str  # 指标名称 (roe, roa, pe_ratio, pb_ratio等)
    operator: ComparisonOperator  # 比较操作符
    value: Any  # 比较值
    period: str = "latest"  # 时间周期 (latest, avg_1y, avg_3y)

    def __post_init__(self):
        if isinstance(self.operator, str):
            self.operator = ComparisonOperator(self.operator)


@dataclass
class TechnicalCriteria:
    """技术指标筛选条件"""
    indicator: str  # 技术指标名称
    operator: ComparisonOperator  # 比较操作符
    value: Any  # 比较值
    period: int = 20  # 计算周期

    def __post_init__(self):
        if isinstance(self.operator, str):
            self.operator = ComparisonOperator(self.operator)


@dataclass
class MarketCriteria:
    """市场指标筛选条件"""
    indicator: str  # 市场指标名称 (market_cap, price, volume等)
    operator: ComparisonOperator  # 比较操作符
    value: Any  # 比较值

    def __post_init__(self):
        if isinstance(self.operator, str):
            self.operator = ComparisonOperator(self.operator)


class ScreeningCriteria:
    """筛选条件管理类"""

    def __init__(self):
        """初始化筛选条件"""
        self.financial_criteria: List[FinancialCriteria] = []
        self.technical_criteria: List[TechnicalCriteria] = []
        self.market_criteria: List[MarketCriteria] = []
        self.custom_criteria: List[Dict[str, Any]] = []

    def add_financial_criteria(self,
                              indicator: str,
                              operator: str,
                              value: Any,
                              period: str = "latest") -> 'ScreeningCriteria':
        """
        添加财务指标筛选条件

        Args:
            indicator: 指标名称
            operator: 比较操作符
            value: 比较值
            period: 时间周期

        Returns:
            自身实例，支持链式调用
        """
        criteria = FinancialCriteria(
            indicator=indicator,
            operator=operator,
            value=value,
            period=period
        )
        self.financial_criteria.append(criteria)
        return self

    def add_roe_criteria(self, min_roe: float = None, max_roe: float = None, period: str = "latest") -> 'ScreeningCriteria':
        """
        添加ROE筛选条件（便捷方法）

        Args:
            min_roe: 最小ROE值（百分比）
            max_roe: 最大ROE值（百分比）
            period: 时间周期

        Returns:
            自身实例，支持链式调用
        """
        if min_roe is not None:
            self.add_financial_criteria("roe", ">=", min_roe, period)
        if max_roe is not None:
            self.add_financial_criteria("roe", "<=", max_roe, period)
        return self

    def clear(self) -> 'ScreeningCriteria':
        """
        清空所有筛选条件

        Returns:
            自身实例，支持链式调用
        """
        self.financial_criteria.clear()
        self.technical_criteria.clear()
        self.market_criteria.clear()
        self.custom_criteria.clear()
        return self