"""
股票筛选器
实现基于多维度条件的股票筛选功能
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Union, Tuple
from loguru import logger

from ..data import DataManager, StockPoolManager, FinancialDataManager
from ..indicators import AlphaIndicatorManager
from .screening_criteria import ScreeningCriteria, ComparisonOperator


class StockScreener:
    """股票筛选器类"""

    def __init__(self, config: Dict):
        """
        初始化股票筛选器

        Args:
            config: 配置字典
        """
        self.config = config
        self.data_manager = DataManager(config)
        self.stock_pool_manager = StockPoolManager(config)
        self.financial_manager = FinancialDataManager(config)
        self.alpha_manager = AlphaIndicatorManager(config)

    def screen_stocks(self,
                     criteria: ScreeningCriteria,
                     stock_pool: List[str] = None,
                     start_time: str = None,
                     end_time: str = None) -> <PERSON><PERSON>[List[str], pd.DataFrame]:
        """
        根据筛选条件筛选股票

        Args:
            criteria: 筛选条件
            stock_pool: 股票池，如果为None则使用配置中的股票池
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            筛选结果：(股票代码列表, 详细数据DataFrame)
        """
        try:
            # 获取股票池
            if stock_pool is None:
                stock_pool = self.stock_pool_manager.get_active_pool()

            if not stock_pool:
                logger.warning("股票池为空")
                return [], pd.DataFrame()

            logger.info(f"开始筛选，初始股票池大小: {len(stock_pool)}")

            # 应用财务指标筛选
            filtered_stocks = self._apply_financial_criteria(
                stock_pool, criteria.financial_criteria, start_time, end_time
            )

            # 获取筛选结果的详细数据
            result_data = self._get_screening_result_data(
                filtered_stocks, start_time, end_time
            )

            logger.info(f"筛选完成，结果股票数量: {len(filtered_stocks)}")
            return filtered_stocks, result_data

        except Exception as e:
            logger.error(f"股票筛选失败: {e}")
            return [], pd.DataFrame()

    def _apply_financial_criteria(self,
                                 stocks: List[str],
                                 criteria: List,
                                 start_time: str = None,
                                 end_time: str = None) -> List[str]:
        """
        应用财务指标筛选条件

        Args:
            stocks: 股票列表
            criteria: 财务指标筛选条件列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            筛选后的股票列表
        """
        if not criteria:
            return stocks

        try:
            filtered_stocks = stocks.copy()

            for criterion in criteria:
                if criterion.indicator == "roe":
                    # ROE筛选
                    filtered_stocks = self.financial_manager.filter_by_roe(
                        filtered_stocks,
                        min_roe=criterion.value if criterion.operator.value == ">=" else None,
                        max_roe=criterion.value if criterion.operator.value == "<=" else None,
                        period=criterion.period
                    )
                else:
                    # 其他财务指标筛选
                    filtered_stocks = self._filter_by_financial_indicator(
                        filtered_stocks, criterion, start_time, end_time
                    )

                logger.info(f"应用财务条件 {criterion.indicator} {criterion.operator.value} {criterion.value} 后，剩余股票: {len(filtered_stocks)}")

            return filtered_stocks

        except Exception as e:
            logger.error(f"应用财务指标筛选失败: {e}")
            return stocks

    def _check_condition(self, value: float, operator: ComparisonOperator, target_value: float) -> bool:
        """
        检查条件是否满足

        Args:
            value: 实际值
            operator: 比较操作符
            target_value: 目标值

        Returns:
            是否满足条件
        """
        if operator == ComparisonOperator.GREATER_THAN:
            return value > target_value
        elif operator == ComparisonOperator.LESS_THAN:
            return value < target_value
        elif operator == ComparisonOperator.GREATER_EQUAL:
            return value >= target_value
        elif operator == ComparisonOperator.LESS_EQUAL:
            return value <= target_value
        elif operator == ComparisonOperator.EQUAL:
            return abs(value - target_value) < 1e-6
        elif operator == ComparisonOperator.NOT_EQUAL:
            return abs(value - target_value) >= 1e-6
        else:
            return False

    def _get_screening_result_data(self,
                                  stocks: List[str],
                                  start_time: str = None,
                                  end_time: str = None) -> pd.DataFrame:
        """
        获取筛选结果的详细数据

        Args:
            stocks: 股票列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            详细数据DataFrame
        """
        if not stocks:
            return pd.DataFrame()

        try:
            # 获取基本价格数据
            price_data = self.data_manager.get_latest_data(stocks, days=1)

            # 获取财务指标数据
            financial_data = self.financial_manager.get_financial_indicators(stocks)

            # 合并数据
            result_data = pd.concat([price_data, financial_data], axis=1)

            return result_data

        except Exception as e:
            logger.error(f"获取筛选结果详细数据失败: {e}")
            return pd.DataFrame()

    def quick_roe_screen(self,
                        min_roe: float = 10.0,
                        max_roe: float = None,
                        period: str = "latest",
                        stock_pool: List[str] = None) -> Tuple[List[str], pd.DataFrame]:
        """
        快速ROE筛选（便捷方法）

        Args:
            min_roe: 最小ROE值（百分比）
            max_roe: 最大ROE值（百分比）
            period: 时间周期
            stock_pool: 股票池

        Returns:
            筛选结果：(股票代码列表, 详细数据DataFrame)
        """
        criteria = ScreeningCriteria().add_roe_criteria(min_roe, max_roe, period)
        return self.screen_stocks(criteria, stock_pool)