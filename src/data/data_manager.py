"""
数据管理器
负责从Qlib获取股票数据
"""

import pandas as pd
from typing import List, Dict, Optional, Union
from datetime import datetime, timedelta
import qlib
from qlib.data import D
from loguru import logger


class DataManager:
    """数据管理器类"""

    def __init__(self, config: Dict):
        """
        初始化数据管理器

        Args:
            config: 配置字典
        """
        self.config = config
        self.data_config = config.get('data', {})

    def get_stock_data(self,
                      instruments: Union[str, List[str]],
                      start_time: str = None,
                      end_time: str = None,
                      fields: List[str] = None) -> pd.DataFrame:
        """
        获取股票数据

        Args:
            instruments: 股票代码或股票代码列表
            start_time: 开始时间
            end_time: 结束时间
            fields: 字段列表

        Returns:
            股票数据DataFrame
        """
        try:
            # 设置默认时间范围
            if start_time is None:
                start_time = self.data_config.get('time_range', {}).get('start_date', '2020-01-01')
            if end_time is None:
                end_time = self.data_config.get('time_range', {}).get('end_date', '2024-12-31')

            # 设置默认字段
            if fields is None:
                fields = ['$open', '$high', '$low', '$close', '$volume', '$factor']

            # 获取数据
            data = D.features(
                instruments=instruments,
                fields=fields,
                start_time=start_time,
                end_time=end_time
            )

            logger.info(f"成功获取股票数据，形状: {data.shape}")
            return data

        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return pd.DataFrame()

    def get_price_data(self,
                      instruments: Union[str, List[str]],
                      start_time: str = None,
                      end_time: str = None) -> pd.DataFrame:
        """
        获取价格数据（开高低收量）

        Args:
            instruments: 股票代码或股票代码列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            价格数据DataFrame
        """
        fields = ['$open', '$high', '$low', '$close', '$volume']
        return self.get_stock_data(instruments, start_time, end_time, fields)

    def get_market_data(self,
                       instruments: Union[str, List[str]],
                       start_time: str = None,
                       end_time: str = None) -> pd.DataFrame:
        """
        获取市场数据（包含复权因子等）

        Args:
            instruments: 股票代码或股票代码列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            市场数据DataFrame
        """
        fields = ['$open', '$high', '$low', '$close', '$volume', '$factor', '$change']
        return self.get_stock_data(instruments, start_time, end_time, fields)

    def get_latest_data(self,
                       instruments: Union[str, List[str]],
                       days: int = 1) -> pd.DataFrame:
        """
        获取最新数据

        Args:
            instruments: 股票代码或股票代码列表
            days: 获取最近几天的数据

        Returns:
            最新数据DataFrame
        """
        end_time = datetime.now().strftime('%Y-%m-%d')
        start_time = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

        return self.get_market_data(instruments, start_time, end_time)

    def get_instruments_list(self, market: str = "all") -> List[str]:
        """
        获取股票代码列表

        Args:
            market: 市场类型 ("all", "sh", "sz")

        Returns:
            股票代码列表
        """
        try:
            if market == "all":
                instruments = D.instruments(market="all")
            else:
                instruments = D.instruments(market=market)

            logger.info(f"获取到 {len(instruments)} 只股票")
            return instruments

        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []