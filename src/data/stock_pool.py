"""
股票池管理器
负责管理沪深300等股票池
"""

import pandas as pd
from typing import List, Dict, Optional
from qlib.data import D
from loguru import logger


class StockPoolManager:
    """股票池管理器类"""

    def __init__(self, config: Dict):
        """
        初始化股票池管理器

        Args:
            config: 配置字典
        """
        self.config = config
        self.data_config = config.get('data', {})
        self.stock_pool_config = self.data_config.get('stock_pool', {})

    def get_hs300_stocks(self, date: str = None) -> List[str]:
        """
        获取沪深300成分股

        Args:
            date: 指定日期，默认为最新

        Returns:
            沪深300股票代码列表
        """
        try:
            # 使用Qlib获取沪深300成分股
            instruments = D.instruments(market="csi300")
            logger.info(f"获取到沪深300成分股 {len(instruments)} 只")
            return instruments

        except Exception as e:
            logger.error(f"获取沪深300成分股失败: {e}")
            # 备用方案：手动定义部分沪深300股票
            return self._get_backup_hs300_stocks()

    def _get_backup_hs300_stocks(self) -> List[str]:
        """
        备用的沪深300股票列表（部分）

        Returns:
            股票代码列表
        """
        # 这里只列出部分代表性股票，实际使用时应该获取完整列表
        backup_stocks = [
            "SH600000",  # 浦发银行
            "SH600036",  # 招商银行
            "SH600519",  # 贵州茅台
            "SH600887",  # 伊利股份
            "SZ000001",  # 平安银行
            "SZ000002",  # 万科A
            "SZ000858",  # 五粮液
            "SZ002415",  # 海康威视
            "SZ300059",  # 东方财富
            "SZ300750",  # 宁德时代
        ]
        logger.warning("使用备用沪深300股票列表")
        return backup_stocks

    def get_custom_pool(self) -> List[str]:
        """
        获取自定义股票池

        Returns:
            自定义股票代码列表
        """
        custom_pool = self.stock_pool_config.get('custom_pool', [])
        logger.info(f"获取到自定义股票池 {len(custom_pool)} 只")
        return custom_pool

    def get_active_pool(self) -> List[str]:
        """
        获取当前激活的股票池

        Returns:
            当前激活的股票代码列表
        """
        stocks = []

        # 如果启用沪深300
        if self.stock_pool_config.get('hs300', False):
            hs300_stocks = self.get_hs300_stocks()
            stocks.extend(hs300_stocks)

        # 添加自定义股票池
        custom_stocks = self.get_custom_pool()
        stocks.extend(custom_stocks)

        # 去重
        stocks = list(set(stocks))

        logger.info(f"当前激活股票池包含 {len(stocks)} 只股票")
        return stocks

    def filter_stocks_by_market_cap(self,
                                   stocks: List[str],
                                   min_cap: float = None,
                                   max_cap: float = None,
                                   date: str = None) -> List[str]:
        """
        按市值筛选股票

        Args:
            stocks: 股票代码列表
            min_cap: 最小市值（亿元）
            max_cap: 最大市值（亿元）
            date: 指定日期

        Returns:
            筛选后的股票代码列表
        """
        try:
            # 获取市值数据
            market_cap_data = D.features(
                instruments=stocks,
                fields=['$market_cap'],
                start_time=date or "2024-01-01",
                end_time=date or "2024-12-31"
            )

            if market_cap_data.empty:
                logger.warning("未获取到市值数据")
                return stocks

            # 筛选逻辑
            filtered_stocks = []
            for stock in stocks:
                try:
                    cap = market_cap_data.loc[stock, '$market_cap'].iloc[-1] / 1e8  # 转换为亿元

                    if min_cap and cap < min_cap:
                        continue
                    if max_cap and cap > max_cap:
                        continue

                    filtered_stocks.append(stock)
                except:
                    continue

            logger.info(f"按市值筛选后剩余 {len(filtered_stocks)} 只股票")
            return filtered_stocks

        except Exception as e:
            logger.error(f"按市值筛选股票失败: {e}")
            return stocks

    def get_stock_info(self, stocks: List[str]) -> pd.DataFrame:
        """
        获取股票基本信息

        Args:
            stocks: 股票代码列表

        Returns:
            股票信息DataFrame
        """
        try:
            # 获取基本信息
            info_data = D.features(
                instruments=stocks,
                fields=['$market_cap', '$pe_ratio', '$pb_ratio'],
                start_time="2024-01-01",
                end_time="2024-12-31"
            )

            return info_data

        except Exception as e:
            logger.error(f"获取股票信息失败: {e}")
            return pd.DataFrame()