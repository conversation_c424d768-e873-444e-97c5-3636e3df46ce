"""
财务数据管理器
负责获取和处理财务指标数据，包括ROE等
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Union
from qlib.data import D
from loguru import logger


class FinancialDataManager:
    """财务数据管理器类"""

    def __init__(self, config: Dict):
        """
        初始化财务数据管理器

        Args:
            config: 配置字典
        """
        self.config = config
        self.data_config = config.get('data', {})
        self.financial_config = self.data_config.get('financial_indicators', {})

    def get_roe_data(self,
                    instruments: Union[str, List[str]],
                    start_time: str = None,
                    end_time: str = None) -> pd.DataFrame:
        """
        获取ROE数据

        Args:
            instruments: 股票代码或股票代码列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            ROE数据DataFrame
        """
        try:
            # 设置默认时间范围
            if start_time is None:
                start_time = self.data_config.get('time_range', {}).get('start_date', '2020-01-01')
            if end_time is None:
                end_time = self.data_config.get('time_range', {}).get('end_date', '2024-12-31')

            # 获取ROE数据
            roe_data = D.features(
                instruments=instruments,
                fields=['$roe'],  # ROE字段
                start_time=start_time,
                end_time=end_time
            )

            logger.info(f"成功获取ROE数据，形状: {roe_data.shape}")
            return roe_data

        except Exception as e:
            logger.error(f"获取ROE数据失败: {e}")
            return pd.DataFrame()

    def get_financial_indicators(self,
                               instruments: Union[str, List[str]],
                               start_time: str = None,
                               end_time: str = None,
                               indicators: List[str] = None) -> pd.DataFrame:
        """
        获取财务指标数据

        Args:
            instruments: 股票代码或股票代码列表
            start_time: 开始时间
            end_time: 结束时间
            indicators: 指标列表

        Returns:
            财务指标数据DataFrame
        """
        try:
            # 设置默认时间范围
            if start_time is None:
                start_time = self.data_config.get('time_range', {}).get('start_date', '2020-01-01')
            if end_time is None:
                end_time = self.data_config.get('time_range', {}).get('end_date', '2024-12-31')

            # 设置默认指标
            if indicators is None:
                indicators = []
                if self.financial_config.get('roe', False):
                    indicators.append('$roe')
                if self.financial_config.get('roa', False):
                    indicators.append('$roa')
                if self.financial_config.get('pe_ratio', False):
                    indicators.append('$pe_ratio')
                if self.financial_config.get('pb_ratio', False):
                    indicators.append('$pb_ratio')

                # 如果没有配置任何指标，使用默认的
                if not indicators:
                    indicators = ['$roe', '$roa', '$pe_ratio', '$pb_ratio']

            # 获取财务指标数据
            financial_data = D.features(
                instruments=instruments,
                fields=indicators,
                start_time=start_time,
                end_time=end_time
            )

            logger.info(f"成功获取财务指标数据，形状: {financial_data.shape}")
            return financial_data

        except Exception as e:
            logger.error(f"获取财务指标数据失败: {e}")
            return pd.DataFrame()

    def filter_by_roe(self,
                     instruments: List[str],
                     min_roe: float = None,
                     max_roe: float = None,
                     period: str = "latest") -> List[str]:
        """
        根据ROE筛选股票

        Args:
            instruments: 股票代码列表
            min_roe: 最小ROE值（百分比）
            max_roe: 最大ROE值（百分比）
            period: 时间周期 ("latest", "avg_1y", "avg_3y")

        Returns:
            筛选后的股票代码列表
        """
        try:
            # 获取ROE数据
            roe_data = self.get_roe_data(instruments)

            if roe_data.empty:
                logger.warning("未获取到ROE数据")
                return instruments

            filtered_stocks = []

            for stock in instruments:
                try:
                    stock_roe_data = roe_data.loc[stock, '$roe']

                    if period == "latest":
                        # 使用最新的ROE值
                        roe_value = stock_roe_data.iloc[-1] * 100  # 转换为百分比
                    elif period == "avg_1y":
                        # 使用最近1年的平均ROE
                        roe_value = stock_roe_data.tail(252).mean() * 100  # 252个交易日约1年
                    elif period == "avg_3y":
                        # 使用最近3年的平均ROE
                        roe_value = stock_roe_data.tail(756).mean() * 100  # 756个交易日约3年
                    else:
                        roe_value = stock_roe_data.iloc[-1] * 100

                    # 检查是否为有效值
                    if pd.isna(roe_value):
                        continue

                    # 应用筛选条件
                    if min_roe is not None and roe_value < min_roe:
                        continue
                    if max_roe is not None and roe_value > max_roe:
                        continue

                    filtered_stocks.append(stock)

                except Exception as e:
                    logger.debug(f"处理股票 {stock} 的ROE数据时出错: {e}")
                    continue

            logger.info(f"根据ROE筛选后剩余 {len(filtered_stocks)} 只股票")
            return filtered_stocks

        except Exception as e:
            logger.error(f"根据ROE筛选股票失败: {e}")
            return instruments