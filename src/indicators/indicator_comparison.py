"""
指标对比模块
提供Alpha360和Alpha158指标的对比分析功能
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Union, Tuple
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from loguru import logger
from .alpha_indicators import AlphaIndicatorManager


class IndicatorComparison:
    """指标对比分析类"""

    def __init__(self, config: Dict):
        """
        初始化指标对比分析器

        Args:
            config: 配置字典
        """
        self.config = config
        self.alpha_manager = AlphaIndicatorManager(config)

    def compare_alpha_features(self,
                              instruments: Union[str, List[str]],
                              alpha360_features: List[str] = None,
                              alpha158_features: List[str] = None,
                              start_time: str = None,
                              end_time: str = None) -> Dict[str, pd.DataFrame]:
        """
        对比Alpha360和Alpha158特征

        Args:
            instruments: 股票代码或股票代码列表
            alpha360_features: Alpha360特征列表
            alpha158_features: Alpha158特征列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            包含对比结果的字典
        """
        results = {}

        try:
            # 获取Alpha360特征
            if self.config.get('indicators', {}).get('alpha360', {}).get('enabled', False):
                alpha360_data = self.alpha_manager.get_selected_features(
                    "alpha360", instruments, alpha360_features, start_time, end_time
                )
                results['alpha360'] = alpha360_data
                logger.info(f"Alpha360特征数据形状: {alpha360_data.shape}")

            # 获取Alpha158特征
            if self.config.get('indicators', {}).get('alpha158', {}).get('enabled', False):
                alpha158_data = self.alpha_manager.get_selected_features(
                    "alpha158", instruments, alpha158_features, start_time, end_time
                )
                results['alpha158'] = alpha158_data
                logger.info(f"Alpha158特征数据形状: {alpha158_data.shape}")

            return results

        except Exception as e:
            logger.error(f"对比Alpha特征失败: {e}")
            return {}