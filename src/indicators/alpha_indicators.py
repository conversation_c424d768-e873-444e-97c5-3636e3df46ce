"""
Alpha指标管理器
实现Alpha360和Alpha158技术指标计算
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Union
from qlib.data import D
from qlib.data.dataset import DatasetH
from qlib.data.dataset.handler import DataHandlerLP
from loguru import logger


class AlphaIndicatorManager:
    """Alpha指标管理器类"""

    def __init__(self, config: Dict):
        """
        初始化Alpha指标管理器

        Args:
            config: 配置字典
        """
        self.config = config
        self.indicators_config = config.get('indicators', {})
        self.alpha360_config = self.indicators_config.get('alpha360', {})
        self.alpha158_config = self.indicators_config.get('alpha158', {})

    def get_alpha360_features(self,
                             instruments: Union[str, List[str]],
                             start_time: str = None,
                             end_time: str = None) -> pd.DataFrame:
        """
        获取Alpha360特征

        Args:
            instruments: 股票代码或股票代码列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            Alpha360特征DataFrame
        """
        try:
            # 设置默认时间范围
            if start_time is None:
                start_time = self.config.get('data', {}).get('time_range', {}).get('start_date', '2020-01-01')
            if end_time is None:
                end_time = self.config.get('data', {}).get('time_range', {}).get('end_date', '2024-12-31')

            # Alpha360特征列表（部分主要特征）
            alpha360_fields = self._get_alpha360_fields()

            # 获取数据
            data = D.features(
                instruments=instruments,
                fields=alpha360_fields,
                start_time=start_time,
                end_time=end_time
            )

            logger.info(f"成功获取Alpha360特征，形状: {data.shape}")
            return data

        except Exception as e:
            logger.error(f"获取Alpha360特征失败: {e}")
            return pd.DataFrame()

    def get_alpha158_features(self,
                             instruments: Union[str, List[str]],
                             start_time: str = None,
                             end_time: str = None) -> pd.DataFrame:
        """
        获取Alpha158特征

        Args:
            instruments: 股票代码或股票代码列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            Alpha158特征DataFrame
        """
        try:
            # 设置默认时间范围
            if start_time is None:
                start_time = self.config.get('data', {}).get('time_range', {}).get('start_date', '2020-01-01')
            if end_time is None:
                end_time = self.config.get('data', {}).get('time_range', {}).get('end_date', '2024-12-31')

            # Alpha158特征列表
            alpha158_fields = self._get_alpha158_fields()

            # 获取数据
            data = D.features(
                instruments=instruments,
                fields=alpha158_fields,
                start_time=start_time,
                end_time=end_time
            )

            logger.info(f"成功获取Alpha158特征，形状: {data.shape}")
            return data

        except Exception as e:
            logger.error(f"获取Alpha158特征失败: {e}")
            return pd.DataFrame()

    def _get_alpha360_fields(self) -> List[str]:
        """
        获取Alpha360特征字段列表

        Returns:
            Alpha360特征字段列表
        """
        # Alpha360的主要特征（简化版本）
        fields = []

        # 基础价格特征
        fields.extend([
            "($close-$open)/$open",  # 日内收益率
            "($high-$low)/$open",    # 日内波动率
            "($close-Ref($close,1))/Ref($close,1)",  # 日收益率
            "($volume/Mean($volume,5)-1)",  # 成交量相对变化
        ])

        # 移动平均特征
        for period in [5, 10, 20, 30, 60]:
            fields.extend([
                f"($close/Mean($close,{period})-1)",  # 价格相对移动平均
                f"(Mean($volume,{period})/Mean($volume,{period*2})-1)",  # 成交量移动平均比
            ])

        # 技术指标特征
        fields.extend([
            "($close-Min($low,9))/(Max($high,9)-Min($low,9))",  # RSV
            "Std($close,20)/$close",  # 价格波动率
            "Corr($close,$volume,10)",  # 价量相关性
        ])

        return fields[:50]  # 返回前50个特征作为示例

    def _get_alpha158_fields(self) -> List[str]:
        """
        获取Alpha158特征字段列表

        Returns:
            Alpha158特征字段列表
        """
        # Alpha158的主要特征
        fields = []

        # KBAR特征 (开高低收成交量)
        fields.extend([
            "$open", "$high", "$low", "$close", "$volume"
        ])

        # 价格变化特征
        for period in [1, 5, 10, 20, 30, 60]:
            fields.extend([
                f"Ref($close,{period})/$close-1",  # 价格变化率
                f"Mean($close,{period})/$close-1",  # 移动平均偏离
                f"Std($close,{period})",  # 标准差
            ])

        # 成交量特征
        for period in [5, 10, 20, 30, 60]:
            fields.extend([
                f"Mean($volume,{period})",  # 成交量移动平均
                f"Std($volume,{period})",   # 成交量标准差
                f"$volume/Mean($volume,{period})",  # 成交量相对强度
            ])

        # 技术指标
        fields.extend([
            "($close-Min($low,9))/(Max($high,9)-Min($low,9))",  # KDJ的RSV
            "($close-Mean($close,20))/Std($close,20)",  # 布林带位置
            "Corr($close,$volume,10)",  # 价量相关性
            "Rank($close,20)",  # 价格排名
        ])

        return fields[:158]  # 返回158个特征

    def get_selected_features(self,
                             feature_type: str,
                             instruments: Union[str, List[str]],
                             feature_names: List[str] = None,
                             start_time: str = None,
                             end_time: str = None) -> pd.DataFrame:
        """
        获取选定的特征

        Args:
            feature_type: 特征类型 ("alpha360", "alpha158")
            instruments: 股票代码或股票代码列表
            feature_names: 指定的特征名称列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            选定特征的DataFrame
        """
        if feature_type == "alpha360":
            if feature_names is None:
                return self.get_alpha360_features(instruments, start_time, end_time)
            else:
                # 获取指定特征
                try:
                    data = D.features(
                        instruments=instruments,
                        fields=feature_names,
                        start_time=start_time or self.config.get('data', {}).get('time_range', {}).get('start_date', '2020-01-01'),
                        end_time=end_time or self.config.get('data', {}).get('time_range', {}).get('end_date', '2024-12-31')
                    )
                    return data
                except Exception as e:
                    logger.error(f"获取指定Alpha360特征失败: {e}")
                    return pd.DataFrame()

        elif feature_type == "alpha158":
            if feature_names is None:
                return self.get_alpha158_features(instruments, start_time, end_time)
            else:
                # 获取指定特征
                try:
                    data = D.features(
                        instruments=instruments,
                        fields=feature_names,
                        start_time=start_time or self.config.get('data', {}).get('time_range', {}).get('start_date', '2020-01-01'),
                        end_time=end_time or self.config.get('data', {}).get('time_range', {}).get('end_date', '2024-12-31')
                    )
                    return data
                except Exception as e:
                    logger.error(f"获取指定Alpha158特征失败: {e}")
                    return pd.DataFrame()

        else:
            logger.error(f"不支持的特征类型: {feature_type}")
            return pd.DataFrame()