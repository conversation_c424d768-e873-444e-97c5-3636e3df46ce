#!/usr/bin/env python3
"""
VenusQuant 快速启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = ['streamlit', 'qlib', 'pandas', 'plotly', 'loguru', 'pyyaml']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_qlib_data():
    """检查Qlib数据是否存在"""
    data_path = Path.home() / ".qlib" / "qlib_data" / "cn_data"
    
    if data_path.exists():
        print("✅ Qlib数据已存在")
        return True
    else:
        print("❌ Qlib数据不存在")
        print("请运行以下命令下载数据:")
        print("python scripts/download_data.py")
        return False

def run_streamlit():
    """运行Streamlit应用"""
    try:
        # 设置环境变量
        os.environ['PYTHONPATH'] = str(Path(__file__).parent / "src")
        
        # 运行Streamlit
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            "src/web/app.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ]
        
        print("🚀 启动VenusQuant Web应用...")
        print("📱 访问地址: http://localhost:8501")
        print("⏹️  按 Ctrl+C 停止应用")
        print("-" * 50)
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 VenusQuant - 量化数据分析系统")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查数据（可选）
    check_qlib_data()
    
    # 运行应用
    run_streamlit()

if __name__ == "__main__":
    main()
