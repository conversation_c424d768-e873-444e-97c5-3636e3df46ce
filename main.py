#!/usr/bin/env python3
"""
VenusQuant - 基于Qlib的量化数据分析显示系统
主程序入口
"""

import os
import sys
import yaml
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

from loguru import logger
import qlib
from qlib.config import REG_CN

def load_config():
    """加载配置文件"""
    config_path = Path(__file__).parent / "config" / "config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def init_qlib(config):
    """初始化Qlib"""
    try:
        qlib.init(
            provider_uri=config['qlib']['provider_uri'],
            region=REG_CN
        )
        logger.info("Qlib初始化成功")
        return True
    except Exception as e:
        logger.error(f"Qlib初始化失败: {e}")
        return False

def main():
    """主函数"""
    # 加载配置
    config = load_config()

    # 设置日志
    log_file = config['logging']['file']
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    logger.add(log_file, level=config['logging']['level'])

    logger.info("VenusQuant 启动中...")

    # 初始化Qlib
    if not init_qlib(config):
        logger.error("系统初始化失败")
        return

    # 启动Web界面
    from web.app import run_app
    run_app(config)

if __name__ == "__main__":
    main()