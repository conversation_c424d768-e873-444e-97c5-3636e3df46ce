# VenusQuant 配置文件

# Qlib 配置
qlib:
  provider_uri: "~/.qlib/qlib_data/cn_data"  # Qlib数据路径
  region: "cn"  # 中国市场

# 数据配置
data:
  # 股票池配置
  stock_pool:
    hs300: true  # 使用沪深300
    custom_pool: []  # 自定义股票池

  # 数据时间范围
  time_range:
    start_date: "2020-01-01"
    end_date: "2024-12-31"

  # 财务指标配置
  financial_indicators:
    roe: true  # ROE指标
    roa: true  # ROA指标
    pe_ratio: true  # 市盈率
    pb_ratio: true  # 市净率

# 技术指标配置
indicators:
  alpha360:
    enabled: true
    features: "all"  # 或指定具体特征列表

  alpha158:
    enabled: true
    features: "all"

# 回测配置
backtest:
  initial_capital: 1000000  # 初始资金100万
  benchmark: "SH000300"  # 沪深300作为基准
  commission: 0.0015  # 手续费
  min_cost: 5  # 最小手续费

# Web界面配置
web:
  host: "localhost"
  port: 8501
  title: "VenusQuant - 量化分析系统"

# 监控配置
monitoring:
  update_interval: 300  # 5分钟更新一次
  alert_threshold: 0.05  # 5%的变化触发告警

# 日志配置
logging:
  level: "INFO"
  file: "logs/venusquant.log"